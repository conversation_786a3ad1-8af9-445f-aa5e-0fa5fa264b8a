"""
自定义意图类型使用示例
演示如何使用自定义意图类型管理功能
"""
import asyncio
import json
from pathlib import Path

# 添加项目根目录到路径
import sys
sys.path.append(str(Path(__file__).parent.parent))

from core.intent_recognition.custom_intent_manager import CustomIntentManager
from model.intent_models import (
    CustomIntentCreateRequest, IntentPatternCreateRequest, 
    IntentExampleCreateRequest, IntentTestRequest
)


async def main():
    """主函数"""
    print("🚀 自定义意图类型管理示例")
    print("=" * 50)
    
    # 创建自定义意图管理器
    config = {
        "storage_type": "json",
        "storage_config": {
            "storage_dir": "./examples/data/custom_intents"
        },
        "cache_ttl": 300
    }
    
    manager = CustomIntentManager(config=config)
    
    # 初始化管理器
    print("📋 初始化自定义意图管理器...")
    success = await manager.initialize()
    if not success:
        print("❌ 初始化失败")
        return
    print("✅ 初始化成功")
    
    # 1. 创建自定义意图类型
    print("\n📝 创建自定义意图类型...")
    
    # 产品咨询意图
    product_intent = CustomIntentCreateRequest(
        name="product_inquiry",
        display_name="产品咨询",
        description="用户询问产品相关信息",
        category="business",
        priority=100,
        patterns=[
            {
                "pattern_type": "keyword",
                "pattern_value": "产品",
                "weight": 1.0,
                "language": "zh"
            },
            {
                "pattern_type": "keyword", 
                "pattern_value": "价格",
                "weight": 1.2,
                "language": "zh"
            },
            {
                "pattern_type": "regex",
                "pattern_value": r"(多少钱|费用|收费)",
                "weight": 1.5,
                "language": "zh"
            }
        ],
        examples=[
            {
                "query_text": "你们的产品怎么样？",
                "expected_confidence": 0.8,
                "is_positive": True,
                "language": "zh"
            },
            {
                "query_text": "产品价格是多少？",
                "expected_confidence": 0.9,
                "is_positive": True,
                "language": "zh"
            },
            {
                "query_text": "今天天气怎么样？",
                "expected_confidence": 0.1,
                "is_positive": False,
                "language": "zh"
            }
        ]
    )
    
    try:
        intent = await manager.create_intent(product_intent, "example_user")
        print(f"✅ 创建产品咨询意图成功: {intent.intent_id}")
    except Exception as e:
        print(f"❌ 创建产品咨询意图失败: {e}")
    
    # 技术支持意图
    support_intent = CustomIntentCreateRequest(
        name="technical_support",
        display_name="技术支持",
        description="用户寻求技术帮助和支持",
        category="support",
        priority=90,
        patterns=[
            {
                "pattern_type": "keyword",
                "pattern_value": "技术支持",
                "weight": 2.0,
                "language": "zh"
            },
            {
                "pattern_type": "keyword",
                "pattern_value": "帮助",
                "weight": 1.0,
                "language": "zh"
            },
            {
                "pattern_type": "regex",
                "pattern_value": r"(问题|故障|错误|bug)",
                "weight": 1.3,
                "language": "zh"
            }
        ],
        examples=[
            {
                "query_text": "我遇到了技术问题，需要帮助",
                "expected_confidence": 0.9,
                "is_positive": True,
                "language": "zh"
            },
            {
                "query_text": "系统出现故障了",
                "expected_confidence": 0.8,
                "is_positive": True,
                "language": "zh"
            }
        ]
    )
    
    try:
        intent = await manager.create_intent(support_intent, "example_user")
        print(f"✅ 创建技术支持意图成功: {intent.intent_id}")
    except Exception as e:
        print(f"❌ 创建技术支持意图失败: {e}")
    
    # 2. 列出所有意图类型
    print("\n📋 列出所有自定义意图类型...")
    intents, total = await manager.list_intents(page=1, page_size=10)
    print(f"📊 总共 {total} 个自定义意图类型:")
    for intent in intents:
        print(f"  - {intent.display_name} ({intent.name}) - 优先级: {intent.priority}")
    
    # 3. 测试意图识别
    print("\n🧪 测试意图识别...")
    
    test_queries = [
        "你们的产品价格是多少？",
        "我需要技术支持",
        "系统出现了bug",
        "今天天气怎么样？",
        "产品功能介绍"
    ]
    
    for query in test_queries:
        print(f"\n🔍 测试查询: '{query}'")
        results = await manager.recognize_intent(query)
        
        if results:
            best_result = results[0]
            print(f"  ✅ 最佳匹配: {best_result.intent_name}")
            print(f"  📊 置信度: {best_result.confidence:.3f}")
            print(f"  💭 推理: {best_result.reasoning}")
        else:
            print("  ❌ 未识别到匹配的意图")
    
    # 4. 获取统计信息
    print("\n📈 获取统计信息...")
    stats = await manager.get_statistics()
    print(f"📊 统计信息:")
    print(f"  - 总意图数量: {stats.total_intents}")
    print(f"  - 启用意图数量: {stats.active_intents}")
    print(f"  - 自定义意图数量: {stats.custom_intents}")
    print(f"  - 总模式数量: {stats.total_patterns}")
    print(f"  - 总示例数量: {stats.total_examples}")
    print(f"  - 分类统计: {stats.categories}")
    
    # 5. 导出数据
    print("\n💾 导出意图数据...")
    export_data = await manager.export_intents()
    
    # 保存到文件
    export_file = Path("./examples/data/exported_intents.json")
    export_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(export_file, 'w', encoding='utf-8') as f:
        json.dump(export_data.dict(), f, ensure_ascii=False, indent=2)
    
    print(f"✅ 数据已导出到: {export_file}")
    print(f"📊 导出统计:")
    print(f"  - 意图数量: {len(export_data.intents)}")
    print(f"  - 模式数量: {len(export_data.patterns)}")
    print(f"  - 示例数量: {len(export_data.examples)}")
    
    print("\n🎉 示例执行完成!")


if __name__ == "__main__":
    asyncio.run(main())
