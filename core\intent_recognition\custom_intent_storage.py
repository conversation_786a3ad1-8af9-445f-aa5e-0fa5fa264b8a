"""
自定义意图类型存储层
支持多种存储后端的数据持久化
"""
import json
import uuid
import asyncio
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
from datetime import datetime

from common.logging_utils import logger_manager
from model.intent_models import (
    CustomIntentType, IntentPattern, IntentExample,
    CustomIntentCreateRequest, CustomIntentUpdateRequest,
    IntentPatternCreateRequest, IntentExampleCreateRequest
)

logger = logger_manager.get_logger("custom_intent_storage")


class BaseIntentStorage:
    """意图存储基类"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
    
    async def initialize(self) -> bool:
        """初始化存储"""
        raise NotImplementedError
    
    # 意图类型CRUD
    async def create_intent(self, intent_data: CustomIntentCreateRequest, created_by: str = None) -> CustomIntentType:
        """创建意图类型"""
        raise NotImplementedError
    
    async def get_intent(self, intent_id: str) -> Optional[CustomIntentType]:
        """获取意图类型"""
        raise NotImplementedError
    
    async def update_intent(self, intent_id: str, update_data: CustomIntentUpdateRequest, updated_by: str = None) -> Optional[CustomIntentType]:
        """更新意图类型"""
        raise NotImplementedError
    
    async def delete_intent(self, intent_id: str) -> bool:
        """删除意图类型"""
        raise NotImplementedError
    
    async def list_intents(self, page: int = 1, page_size: int = 20, category: str = None, is_active: bool = None) -> Tuple[List[CustomIntentType], int]:
        """列出意图类型"""
        raise NotImplementedError
    
    # 意图模式CRUD
    async def create_pattern(self, intent_id: str, pattern_data: IntentPatternCreateRequest) -> IntentPattern:
        """创建意图模式"""
        raise NotImplementedError
    
    async def get_patterns(self, intent_id: str) -> List[IntentPattern]:
        """获取意图模式"""
        raise NotImplementedError
    
    async def update_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> Optional[IntentPattern]:
        """更新意图模式"""
        raise NotImplementedError
    
    async def delete_pattern(self, pattern_id: str) -> bool:
        """删除意图模式"""
        raise NotImplementedError
    
    # 意图示例CRUD
    async def create_example(self, intent_id: str, example_data: IntentExampleCreateRequest) -> IntentExample:
        """创建意图示例"""
        raise NotImplementedError
    
    async def get_examples(self, intent_id: str) -> List[IntentExample]:
        """获取意图示例"""
        raise NotImplementedError
    
    async def delete_example(self, example_id: str) -> bool:
        """删除意图示例"""
        raise NotImplementedError


class JsonFileIntentStorage(BaseIntentStorage):
    """基于JSON文件的意图存储实现"""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__(config)
        self.storage_dir = Path(config.get("storage_dir", "./data/custom_intents"))
        self.intents_file = self.storage_dir / "intents.json"
        self.patterns_file = self.storage_dir / "patterns.json"
        self.examples_file = self.storage_dir / "examples.json"
        self._lock = asyncio.Lock()
    
    async def initialize(self) -> bool:
        """初始化存储"""
        try:
            # 创建存储目录
            self.storage_dir.mkdir(parents=True, exist_ok=True)
            
            # 初始化文件
            for file_path in [self.intents_file, self.patterns_file, self.examples_file]:
                if not file_path.exists():
                    await self._write_json_file(file_path, [])
            
            logger.info(f"JSON文件存储初始化完成: {self.storage_dir}")
            return True
            
        except Exception as e:
            logger.error(f"JSON文件存储初始化失败: {e}")
            return False
    
    async def _read_json_file(self, file_path: Path) -> List[Dict]:
        """读取JSON文件"""
        try:
            if not file_path.exists():
                return []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"读取JSON文件失败 {file_path}: {e}")
            return []
    
    async def _write_json_file(self, file_path: Path, data: List[Dict]):
        """写入JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"写入JSON文件失败 {file_path}: {e}")
            raise
    
    def _generate_id(self) -> str:
        """生成唯一ID"""
        return str(uuid.uuid4())
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        return datetime.now().isoformat()
    
    async def create_intent(self, intent_data: CustomIntentCreateRequest, created_by: str = None) -> CustomIntentType:
        """创建意图类型"""
        async with self._lock:
            intents = await self._read_json_file(self.intents_file)
            
            # 检查名称是否已存在
            if any(intent["name"] == intent_data.name for intent in intents):
                raise ValueError(f"意图类型名称已存在: {intent_data.name}")
            
            # 创建新意图
            intent_id = self._generate_id()
            current_time = self._get_current_time()
            
            new_intent = CustomIntentType(
                intent_id=intent_id,
                name=intent_data.name,
                display_name=intent_data.display_name,
                description=intent_data.description,
                category=intent_data.category,
                priority=intent_data.priority,
                is_active=True,
                is_system=False,
                created_by=created_by,
                created_at=current_time,
                metadata=intent_data.metadata
            )
            
            intents.append(new_intent.dict())
            await self._write_json_file(self.intents_file, intents)
            
            # 创建关联的模式和示例
            for pattern_data in intent_data.patterns:
                pattern_req = IntentPatternCreateRequest(**pattern_data)
                await self.create_pattern(intent_id, pattern_req)
            
            for example_data in intent_data.examples:
                example_req = IntentExampleCreateRequest(**example_data)
                await self.create_example(intent_id, example_req)
            
            logger.info(f"创建意图类型成功: {intent_id}")
            return new_intent
    
    async def get_intent(self, intent_id: str) -> Optional[CustomIntentType]:
        """获取意图类型"""
        intents = await self._read_json_file(self.intents_file)
        
        for intent_data in intents:
            if intent_data["intent_id"] == intent_id:
                return CustomIntentType(**intent_data)
        
        return None
    
    async def update_intent(self, intent_id: str, update_data: CustomIntentUpdateRequest, updated_by: str = None) -> Optional[CustomIntentType]:
        """更新意图类型"""
        async with self._lock:
            intents = await self._read_json_file(self.intents_file)
            
            for i, intent_data in enumerate(intents):
                if intent_data["intent_id"] == intent_id:
                    # 更新字段
                    update_dict = update_data.dict(exclude_unset=True)
                    intent_data.update(update_dict)
                    intent_data["updated_by"] = updated_by
                    intent_data["updated_at"] = self._get_current_time()
                    
                    await self._write_json_file(self.intents_file, intents)
                    
                    logger.info(f"更新意图类型成功: {intent_id}")
                    return CustomIntentType(**intent_data)
            
            return None
    
    async def delete_intent(self, intent_id: str) -> bool:
        """删除意图类型"""
        async with self._lock:
            intents = await self._read_json_file(self.intents_file)
            
            # 删除意图
            original_count = len(intents)
            intents = [intent for intent in intents if intent["intent_id"] != intent_id]
            
            if len(intents) == original_count:
                return False
            
            await self._write_json_file(self.intents_file, intents)
            
            # 删除关联的模式和示例
            patterns = await self._read_json_file(self.patterns_file)
            patterns = [p for p in patterns if p["intent_id"] != intent_id]
            await self._write_json_file(self.patterns_file, patterns)
            
            examples = await self._read_json_file(self.examples_file)
            examples = [e for e in examples if e["intent_id"] != intent_id]
            await self._write_json_file(self.examples_file, examples)
            
            logger.info(f"删除意图类型成功: {intent_id}")
            return True
    
    async def list_intents(self, page: int = 1, page_size: int = 20, category: str = None, is_active: bool = None) -> Tuple[List[CustomIntentType], int]:
        """列出意图类型"""
        intents = await self._read_json_file(self.intents_file)
        
        # 过滤
        filtered_intents = []
        for intent_data in intents:
            if category and intent_data.get("category") != category:
                continue
            if is_active is not None and intent_data.get("is_active") != is_active:
                continue
            filtered_intents.append(intent_data)
        
        # 排序
        filtered_intents.sort(key=lambda x: x.get("priority", 100))
        
        # 分页
        total = len(filtered_intents)
        start = (page - 1) * page_size
        end = start + page_size
        page_intents = filtered_intents[start:end]
        
        result = [CustomIntentType(**intent_data) for intent_data in page_intents]
        return result, total

    async def create_pattern(self, intent_id: str, pattern_data: IntentPatternCreateRequest) -> IntentPattern:
        """创建意图模式"""
        async with self._lock:
            patterns = await self._read_json_file(self.patterns_file)

            pattern_id = self._generate_id()
            current_time = self._get_current_time()

            new_pattern = IntentPattern(
                pattern_id=pattern_id,
                intent_id=intent_id,
                pattern_type=pattern_data.pattern_type,
                pattern_value=pattern_data.pattern_value,
                weight=pattern_data.weight,
                is_active=True,
                language=pattern_data.language,
                created_at=current_time
            )

            patterns.append(new_pattern.dict())
            await self._write_json_file(self.patterns_file, patterns)

            logger.info(f"创建意图模式成功: {pattern_id}")
            return new_pattern

    async def get_patterns(self, intent_id: str) -> List[IntentPattern]:
        """获取意图模式"""
        patterns = await self._read_json_file(self.patterns_file)

        result = []
        for pattern_data in patterns:
            if pattern_data["intent_id"] == intent_id:
                result.append(IntentPattern(**pattern_data))

        return result

    async def update_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> Optional[IntentPattern]:
        """更新意图模式"""
        async with self._lock:
            patterns = await self._read_json_file(self.patterns_file)

            for i, pattern in enumerate(patterns):
                if pattern["pattern_id"] == pattern_id:
                    pattern.update(pattern_data)
                    pattern["updated_at"] = self._get_current_time()

                    await self._write_json_file(self.patterns_file, patterns)

                    logger.info(f"更新意图模式成功: {pattern_id}")
                    return IntentPattern(**pattern)

            return None

    async def delete_pattern(self, pattern_id: str) -> bool:
        """删除意图模式"""
        async with self._lock:
            patterns = await self._read_json_file(self.patterns_file)

            original_count = len(patterns)
            patterns = [p for p in patterns if p["pattern_id"] != pattern_id]

            if len(patterns) == original_count:
                return False

            await self._write_json_file(self.patterns_file, patterns)

            logger.info(f"删除意图模式成功: {pattern_id}")
            return True

    async def create_example(self, intent_id: str, example_data: IntentExampleCreateRequest) -> IntentExample:
        """创建意图示例"""
        async with self._lock:
            examples = await self._read_json_file(self.examples_file)

            example_id = self._generate_id()
            current_time = self._get_current_time()

            new_example = IntentExample(
                example_id=example_id,
                intent_id=intent_id,
                query_text=example_data.query_text,
                expected_confidence=example_data.expected_confidence,
                is_positive=example_data.is_positive,
                language=example_data.language,
                created_at=current_time
            )

            examples.append(new_example.dict())
            await self._write_json_file(self.examples_file, examples)

            logger.info(f"创建意图示例成功: {example_id}")
            return new_example

    async def get_examples(self, intent_id: str) -> List[IntentExample]:
        """获取意图示例"""
        examples = await self._read_json_file(self.examples_file)

        result = []
        for example_data in examples:
            if example_data["intent_id"] == intent_id:
                result.append(IntentExample(**example_data))

        return result

    async def delete_example(self, example_id: str) -> bool:
        """删除意图示例"""
        async with self._lock:
            examples = await self._read_json_file(self.examples_file)

            original_count = len(examples)
            examples = [e for e in examples if e["example_id"] != example_id]

            if len(examples) == original_count:
                return False

            await self._write_json_file(self.examples_file, examples)

            logger.info(f"删除意图示例成功: {example_id}")
            return True


# 存储工厂
class IntentStorageFactory:
    """意图存储工厂"""

    @staticmethod
    def create_storage(storage_type: str = "json", config: Dict[str, Any] = None) -> BaseIntentStorage:
        """创建存储实例"""
        if storage_type == "json":
            return JsonFileIntentStorage(config)
        else:
            raise ValueError(f"不支持的存储类型: {storage_type}")


# 导出
__all__ = [
    "BaseIntentStorage",
    "JsonFileIntentStorage",
    "IntentStorageFactory"
]
