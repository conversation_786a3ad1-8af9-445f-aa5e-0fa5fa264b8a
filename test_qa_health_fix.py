#!/usr/bin/env python3
"""
测试问答系统健康检查修复的脚本
"""

import requests
import json
import time

def test_qa_health():
    """测试问答系统健康检查"""
    base_url = "http://localhost:8002"

    print("🎯 测试问答系统健康检查修复")
    print("=" * 50)

    # 首先检查服务器是否可达
    print("🔍 检查服务器连接...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        print(f"✅ 服务器连接正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        print("请确保服务器正在运行在 localhost:8002")
        return

    # 测试健康检查
    print("\n🔍 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/api/v1/qa/health", timeout=10)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 健康检查成功")
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 健康检查异常: {e}")

    # 测试统计信息
    print("\n🔍 测试统计信息...")
    try:
        response = requests.get(f"{base_url}/api/v1/qa/statistics", timeout=10)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 统计信息获取成功")
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 统计信息获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 统计信息获取异常: {e}")

    # 测试分类列表
    print("\n🔍 测试分类列表...")
    try:
        response = requests.get(f"{base_url}/api/v1/qa/categories", timeout=10)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ 分类列表获取成功")
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 分类列表获取失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 分类列表获取异常: {e}")

if __name__ == "__main__":
    test_qa_health()
