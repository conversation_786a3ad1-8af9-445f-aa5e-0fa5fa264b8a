from fastapi import APIRouter, HTTPException, Request
from api.extensive_api import TransmitOpenAIPortAPI

# 创建路由器
router = APIRouter(prefix="/api", tags=["原生端口转发"])

# 创建API处理器实例
transmit_openai_port_api = TransmitOpenAIPortAPI()


@router.api_route(
    "/v1/chat/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
    tags=["接口转发"],
    summary="转发到LLM模型服务的所有接口",
    description="""
    转发所有 /v1/* 路径的请求到 http://localhost:8100/v1/* 服务
    
    **支持的功能：**
    - 转发所有HTTP方法（GET、POST、PUT、DELETE等）
    - 保持原始请求头和查询参数
    - 转发请求体（对于POST/PUT等）
    - 完整转发响应内容和状态码
    - 支持流式响应
    
    **使用示例：**
    - `/v1/chat/completions` → `http://localhost:8100/v1/chat/completions`
    - `/v1/models` → `http://localhost:8100/v1/models`
    """
)
async def forward_to_llm_service(path: str, request: Request):
    """转发所有v1路径请求到LLM服务"""
    return await transmit_openai_port_api.forward_to_llm_service(path, request)

@router.api_route(
    "/v1/embedding/{path:path}",
    methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"],
    tags=["接口转发"],
    summary="转发到LLM模型服务的所有接口",
    description="""
    转发所有 /v1/* 路径的请求到 http://localhost:8100/v1/* 服务
    
    **支持的功能：**
    - 转发所有HTTP方法（GET、POST、PUT、DELETE等）
    - 保持原始请求头和查询参数
    - 转发请求体（对于POST/PUT等）
    - 完整转发响应内容和状态码
    - 支持流式响应
    
    **使用示例：**
    - `/v1/chat/completions` → `http://localhost:8100/v1/chat/completions`
    - `/v1/models` → `http://localhost:8100/v1/models`
    """
)
async def forward_to_embedding_service(path: str, request: Request):
    """转发所有v1路径请求到Embedding服务"""
    return await transmit_openai_port_api.forward_to_llm_service(path, request)


# 导出路由器
__all__ = ["router"]
