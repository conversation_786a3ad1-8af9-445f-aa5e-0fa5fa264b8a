# 自定义意图类型管理

## 概述

自定义意图类型管理功能允许用户创建、管理和使用自定义的意图识别类型，扩展系统的意图识别能力。该功能支持完整的增删改查操作，并提供了灵活的模式匹配和示例管理。

## 功能特性

- ✅ **完整的CRUD操作**: 创建、读取、更新、删除自定义意图类型
- ✅ **多种匹配模式**: 支持关键词、正则表达式、语义匹配
- ✅ **示例管理**: 为每个意图类型添加正例和负例示例
- ✅ **优先级控制**: 设置意图识别的优先级
- ✅ **分类管理**: 按业务分类组织意图类型
- ✅ **批量操作**: 支持批量启用、禁用、删除操作
- ✅ **数据导入导出**: 支持意图数据的备份和迁移
- ✅ **实时测试**: 提供意图识别测试功能
- ✅ **统计分析**: 提供详细的统计信息

## 架构设计

```
自定义意图类型管理系统
├── 数据模型层 (model/intent_models.py)
│   ├── CustomIntentType - 意图类型模型
│   ├── IntentPattern - 识别模式模型
│   ├── IntentExample - 示例数据模型
│   └── 各种请求/响应模型
├── 存储层 (core/intent_recognition/custom_intent_storage.py)
│   ├── BaseIntentStorage - 存储基类
│   ├── JsonFileIntentStorage - JSON文件存储实现
│   └── IntentStorageFactory - 存储工厂
├── 业务逻辑层 (core/intent_recognition/custom_intent_manager.py)
│   └── CustomIntentManager - 核心管理器
├── API层 (api/custom_intent_api.py)
│   └── 各种API处理函数
└── 路由层 (routers/custom_intent_router.py)
    └── FastAPI路由定义
```

## API 端点

### 基础路径
所有自定义意图类型相关的API都以 `/api/v1/custom-intents` 为基础路径。

### 主要端点

#### 1. 意图类型管理
- `GET /health` - 健康检查
- `POST /intents` - 创建自定义意图类型
- `GET /intents` - 获取意图类型列表
- `GET /intents/{intent_id}` - 获取意图类型详情
- `PUT /intents/{intent_id}` - 更新意图类型
- `DELETE /intents/{intent_id}` - 删除意图类型

#### 2. 模式管理
- `POST /intents/{intent_id}/patterns` - 添加识别模式
- `GET /intents/{intent_id}/patterns` - 获取模式列表
- `PUT /patterns/{pattern_id}` - 更新模式
- `DELETE /patterns/{pattern_id}` - 删除模式

#### 3. 示例管理
- `POST /intents/{intent_id}/examples` - 添加示例
- `GET /intents/{intent_id}/examples` - 获取示例列表
- `DELETE /examples/{example_id}` - 删除示例

#### 4. 测试和统计
- `POST /test` - 测试意图识别
- `GET /statistics` - 获取统计信息
- `POST /batch` - 批量操作
- `POST /export` - 导出数据
- `POST /import` - 导入数据

## 使用示例

### 1. 创建自定义意图类型

```python
import requests

# 创建产品咨询意图
intent_data = {
    "name": "product_inquiry",
    "display_name": "产品咨询",
    "description": "用户询问产品相关信息",
    "category": "business",
    "priority": 100,
    "patterns": [
        {
            "pattern_type": "keyword",
            "pattern_value": "产品",
            "weight": 1.0
        },
        {
            "pattern_type": "regex",
            "pattern_value": r"(价格|费用|多少钱)",
            "weight": 1.5
        }
    ],
    "examples": [
        {
            "query_text": "你们的产品怎么样？",
            "expected_confidence": 0.8,
            "is_positive": True
        }
    ]
}

response = requests.post(
    "http://localhost:8002/api/v1/custom-intents/intents",
    json=intent_data
)
print(response.json())
```

### 2. 测试意图识别

```python
test_data = {
    "query_text": "你们的产品价格是多少？",
    "include_system": True,
    "include_custom": True
}

response = requests.post(
    "http://localhost:8002/api/v1/custom-intents/test",
    json=test_data
)
print(response.json())
```

### 3. 获取统计信息

```python
response = requests.get(
    "http://localhost:8002/api/v1/custom-intents/statistics"
)
print(response.json())
```

## 模式类型说明

### 1. 关键词匹配 (keyword)
- **描述**: 简单的关键词包含匹配
- **示例**: `"产品"` 匹配包含"产品"的查询
- **权重**: 建议 0.5-2.0

### 2. 正则表达式匹配 (regex)
- **描述**: 使用正则表达式进行复杂模式匹配
- **示例**: `r"(价格|费用|多少钱)"` 匹配价格相关询问
- **权重**: 建议 1.0-3.0

### 3. 语义匹配 (semantic)
- **描述**: 基于语义相似度的匹配（当前简化实现）
- **示例**: `"产品咨询 价格询问"` 语义相关的查询
- **权重**: 建议 0.8-2.0

## 最佳实践

### 1. 意图设计原则
- **明确性**: 每个意图应该有明确的业务含义
- **互斥性**: 不同意图之间应该尽量避免重叠
- **完整性**: 覆盖主要的业务场景

### 2. 模式配置建议
- **多样性**: 为每个意图配置多种类型的模式
- **权重平衡**: 根据模式的准确性调整权重
- **测试验证**: 使用示例数据验证模式效果

### 3. 示例数据管理
- **正负例平衡**: 既要有正例也要有负例
- **覆盖性**: 示例应该覆盖各种表达方式
- **质量控制**: 定期审查和更新示例数据

### 4. 性能优化
- **缓存策略**: 系统自动缓存意图数据，减少查询延迟
- **优先级设置**: 合理设置优先级，提高识别效率
- **定期清理**: 删除不再使用的意图类型

## 集成到意图识别流程

自定义意图类型已经集成到主要的意图识别流程中：

1. **优先级顺序**: 大模型 → 自定义意图 → 系统内置规则
2. **置信度阈值**: 自定义意图置信度 > 0.5 时才会被采用
3. **回退机制**: 如果自定义意图识别失败，会回退到系统默认流程

## 数据存储

### 默认存储方式
- **存储类型**: JSON文件存储
- **存储位置**: `./data/custom_intents/`
- **文件结构**:
  - `intents.json` - 意图类型数据
  - `patterns.json` - 模式数据
  - `examples.json` - 示例数据

### 扩展存储支持
系统设计支持多种存储后端，可以通过配置切换：
- JSON文件存储（默认）
- 数据库存储（待实现）
- Redis存储（待实现）

## 故障排除

### 常见问题

1. **意图识别不准确**
   - 检查模式配置是否合理
   - 增加更多示例数据
   - 调整模式权重

2. **创建意图失败**
   - 检查意图名称是否唯一
   - 验证正则表达式语法
   - 确认存储目录权限

3. **性能问题**
   - 检查缓存配置
   - 减少不必要的模式数量
   - 优化正则表达式复杂度

### 日志查看
系统会记录详细的操作日志，可以通过以下方式查看：
```bash
# 查看意图管理相关日志
grep "custom_intent" logs/app.log
```

## 版本更新

### v1.0.0 (当前版本)
- ✅ 基础CRUD功能
- ✅ 多种模式匹配
- ✅ 示例管理
- ✅ 数据导入导出
- ✅ 统计分析

### 计划功能
- 🔄 机器学习模型训练
- 🔄 A/B测试支持
- 🔄 可视化管理界面
- 🔄 更多存储后端支持
