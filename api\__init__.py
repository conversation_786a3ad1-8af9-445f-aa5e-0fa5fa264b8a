"""
API处理器模块
统一导出所有API处理器
"""

# 导入所有API处理器
from .document_api import DocumentAPI
from .query_api import QueryAPI
from .knowledge_graph_api import KnowledgeGraphAPI
from .intent_recogition_api import (
    set_llm_function,
    get_query_processor,
    health_check_api,
    analyze_intent_api,
    safety_check_api,
    get_status_api,
    get_intent_types_api,
    get_safety_levels_api
)
from .system_api import SystemAPI
from .extensive_api import TransmitOpenAIPortAPI
from .knowledge_base_api import KnowledgeBaseAPI
from .cache_management_api import CacheManagementAPI
from .custom_intent_api import (
    get_custom_intent_manager,
    create_intent_api,
    get_intent_api,
    update_intent_api,
    delete_intent_api,
    list_intents_api,
    add_pattern_api,
    get_patterns_api,
    update_pattern_api,
    delete_pattern_api,
    add_example_api,
    get_examples_api,
    delete_example_api,
    test_intent_api,
    get_statistics_api,
    batch_operation_api,
    export_intents_api,
    import_intents_api,
    health_check_api as custom_intent_health_check_api
)

# 导出所有API处理器
__all__ = [
    "DocumentAPI",
    "QueryAPI",
    "KnowledgeGraphAPI",
    "SystemAPI",
    "KnowledgeBaseAPI",
    "CacheManagementAPI",
    # 意图识别API
    "set_llm_function",
    "get_query_processor",
    "health_check_api",
    "analyze_intent_api",
    "safety_check_api",
    "get_status_api",
    "get_intent_types_api",
    "get_safety_levels_api",

    # 自定义意图API
    "get_custom_intent_manager",
    "create_intent_api",
    "get_intent_api",
    "update_intent_api",
    "delete_intent_api",
    "list_intents_api",
    "add_pattern_api",
    "get_patterns_api",
    "update_pattern_api",
    "delete_pattern_api",
    "add_example_api",
    "get_examples_api",
    "delete_example_api",
    "test_intent_api",
    "get_statistics_api",
    "batch_operation_api",
    "export_intents_api",
    "import_intents_api",
    "custom_intent_health_check_api"
    
    # 其他额外使用
    "TransmitOpenAIPortAPI"
]