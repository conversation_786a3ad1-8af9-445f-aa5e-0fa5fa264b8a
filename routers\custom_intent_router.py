"""
自定义意图类型路由
提供自定义意图类型管理的REST API端点
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Query, Body
from fastapi.responses import JSONResponse

from model.base_models import BaseResponse
from model.intent_models import (
    CustomIntentCreateRequest, CustomIntentUpdateRequest,
    IntentPatternCreateRequest, IntentExampleCreateRequest,
    IntentTestRequest, IntentBatchOperation, IntentImportRequest,
    CustomIntentListResponse, IntentPatternListResponse, IntentExampleListResponse,
    IntentTestResponse, IntentStatistics, IntentExportData
)
from api.custom_intent_api import (
    create_intent_api, get_intent_api, update_intent_api, delete_intent_api, list_intents_api,
    add_pattern_api, get_patterns_api, update_pattern_api, delete_pattern_api,
    add_example_api, get_examples_api, delete_example_api,
    test_intent_api, get_statistics_api, batch_operation_api,
    export_intents_api, import_intents_api, health_check_api
)
from common.logging_utils import logger_manager

logger = logger_manager.get_logger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/v1/custom-intents", tags=["自定义意图类型"])


# 健康检查
@router.get(
    "/health",
    response_model=BaseResponse,
    summary="自定义意图服务健康检查",
    description="检查自定义意图类型管理服务的运行状态"
)
async def health_check():
    """自定义意图服务健康检查"""
    try:
        result = await health_check_api()
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 意图类型管理
@router.post(
    "/intents",
    response_model=BaseResponse,
    summary="创建自定义意图类型",
    description="""
    创建新的自定义意图类型。
    
    **参数说明：**
    - name: 意图类型名称（唯一标识）
    - display_name: 显示名称
    - description: 意图描述
    - category: 意图分类
    - priority: 优先级（1-1000）
    - patterns: 识别模式列表
    - examples: 示例数据列表
    
    **模式类型：**
    - keyword: 关键词匹配
    - regex: 正则表达式匹配
    - semantic: 语义匹配
    
    **使用示例：**
    ```json
    {
        "name": "product_inquiry",
        "display_name": "产品咨询",
        "description": "用户询问产品相关信息",
        "category": "business",
        "priority": 100,
        "patterns": [
            {
                "pattern_type": "keyword",
                "pattern_value": "产品",
                "weight": 1.0
            }
        ],
        "examples": [
            {
                "query_text": "你们的产品怎么样？",
                "expected_confidence": 0.8
            }
        ]
    }
    ```
    """
)
async def create_intent(
    intent_data: CustomIntentCreateRequest,
    created_by: Optional[str] = Query(None, description="创建者")
):
    """创建自定义意图类型"""
    try:
        result = await create_intent_api(intent_data.dict(), created_by)
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"创建意图类型失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/intents/{intent_id}",
    response_model=BaseResponse,
    summary="获取自定义意图类型详情",
    description="根据意图ID获取自定义意图类型的详细信息"
)
async def get_intent(intent_id: str):
    """获取自定义意图类型详情"""
    try:
        result = await get_intent_api(intent_id)
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        return BaseResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取意图类型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put(
    "/intents/{intent_id}",
    response_model=BaseResponse,
    summary="更新自定义意图类型",
    description="更新指定的自定义意图类型信息"
)
async def update_intent(
    intent_id: str,
    update_data: CustomIntentUpdateRequest,
    updated_by: Optional[str] = Query(None, description="更新者")
):
    """更新自定义意图类型"""
    try:
        result = await update_intent_api(intent_id, update_data.dict(exclude_unset=True), updated_by)
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        return BaseResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新意图类型失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.delete(
    "/intents/{intent_id}",
    response_model=BaseResponse,
    summary="删除自定义意图类型",
    description="删除指定的自定义意图类型及其关联的模式和示例"
)
async def delete_intent(intent_id: str):
    """删除自定义意图类型"""
    try:
        result = await delete_intent_api(intent_id)
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        return BaseResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除意图类型失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/intents",
    response_model=BaseResponse,
    summary="获取自定义意图类型列表",
    description="""
    获取自定义意图类型列表，支持分页和过滤。
    
    **查询参数：**
    - page: 页码（从1开始）
    - page_size: 每页大小（1-100）
    - category: 按分类过滤
    - is_active: 按状态过滤（true/false）
    
    **返回数据：**
    - items: 意图类型列表
    - pagination: 分页信息
    - total_count: 总数量
    - active_count: 启用数量
    """
)
async def list_intents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    category: Optional[str] = Query(None, description="分类过滤"),
    is_active: Optional[bool] = Query(None, description="状态过滤")
):
    """获取自定义意图类型列表"""
    try:
        result = await list_intents_api(page, page_size, category, is_active)
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"获取意图类型列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 意图模式管理
@router.post(
    "/intents/{intent_id}/patterns",
    response_model=BaseResponse,
    summary="添加意图识别模式",
    description="""
    为指定意图类型添加识别模式。
    
    **模式类型：**
    - keyword: 关键词匹配
    - regex: 正则表达式匹配
    - semantic: 语义匹配
    
    **参数说明：**
    - pattern_type: 模式类型
    - pattern_value: 模式值
    - weight: 权重（0.0-10.0）
    - language: 语言代码
    """
)
async def add_pattern(intent_id: str, pattern_data: IntentPatternCreateRequest):
    """添加意图识别模式"""
    try:
        result = await add_pattern_api(intent_id, pattern_data.dict())
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"添加意图模式失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/intents/{intent_id}/patterns",
    response_model=BaseResponse,
    summary="获取意图识别模式列表",
    description="获取指定意图类型的所有识别模式"
)
async def get_patterns(intent_id: str):
    """获取意图识别模式列表"""
    try:
        result = await get_patterns_api(intent_id)
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"获取意图模式失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put(
    "/patterns/{pattern_id}",
    response_model=BaseResponse,
    summary="更新意图识别模式",
    description="更新指定的意图识别模式"
)
async def update_pattern(
    pattern_id: str,
    pattern_data: Dict[str, Any] = Body(..., description="更新数据")
):
    """更新意图识别模式"""
    try:
        result = await update_pattern_api(pattern_id, pattern_data)
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        return BaseResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新意图模式失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.delete(
    "/patterns/{pattern_id}",
    response_model=BaseResponse,
    summary="删除意图识别模式",
    description="删除指定的意图识别模式"
)
async def delete_pattern(pattern_id: str):
    """删除意图识别模式"""
    try:
        result = await delete_pattern_api(pattern_id)
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        return BaseResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除意图模式失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# 意图示例管理
@router.post(
    "/intents/{intent_id}/examples",
    response_model=BaseResponse,
    summary="添加意图示例",
    description="""
    为指定意图类型添加示例数据。
    
    **参数说明：**
    - query_text: 查询文本
    - expected_confidence: 期望置信度（0.0-1.0）
    - is_positive: 是否为正例
    - language: 语言代码
    """
)
async def add_example(intent_id: str, example_data: IntentExampleCreateRequest):
    """添加意图示例"""
    try:
        result = await add_example_api(intent_id, example_data.dict())
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"添加意图示例失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/intents/{intent_id}/examples",
    response_model=BaseResponse,
    summary="获取意图示例列表",
    description="获取指定意图类型的所有示例数据"
)
async def get_examples(intent_id: str):
    """获取意图示例列表"""
    try:
        result = await get_examples_api(intent_id)
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"获取意图示例失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete(
    "/examples/{example_id}",
    response_model=BaseResponse,
    summary="删除意图示例",
    description="删除指定的意图示例"
)
async def delete_example(example_id: str):
    """删除意图示例"""
    try:
        result = await delete_example_api(example_id)
        if not result["success"]:
            raise HTTPException(status_code=404, detail=result["message"])
        return BaseResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除意图示例失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# 意图测试和统计
@router.post(
    "/test",
    response_model=BaseResponse,
    summary="测试意图识别",
    description="""
    测试查询文本的意图识别效果。

    **参数说明：**
    - query_text: 测试查询文本
    - intent_ids: 指定测试的意图ID列表（可选）
    - include_system: 是否包含系统内置意图
    - include_custom: 是否包含自定义意图

    **返回数据：**
    - query_text: 测试查询文本
    - results: 识别结果列表
    - best_match: 最佳匹配结果
    - processing_time: 处理时间
    """
)
async def test_intent(test_data: IntentTestRequest):
    """测试意图识别"""
    try:
        result = await test_intent_api(test_data.dict())
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"意图测试失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get(
    "/statistics",
    response_model=BaseResponse,
    summary="获取意图统计信息",
    description="""
    获取自定义意图类型的统计信息。

    **返回数据：**
    - total_intents: 总意图数量
    - active_intents: 启用意图数量
    - custom_intents: 自定义意图数量
    - system_intents: 系统意图数量
    - total_patterns: 总模式数量
    - total_examples: 总示例数量
    - categories: 分类统计
    """
)
async def get_statistics():
    """获取意图统计信息"""
    try:
        result = await get_statistics_api()
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/batch",
    response_model=BaseResponse,
    summary="批量操作意图类型",
    description="""
    对多个意图类型执行批量操作。

    **操作类型：**
    - activate: 批量启用
    - deactivate: 批量禁用
    - delete: 批量删除
    - export: 批量导出

    **参数说明：**
    - operation: 操作类型
    - intent_ids: 意图ID列表
    - options: 操作选项（可选）
    """
)
async def batch_operation(operation_data: IntentBatchOperation):
    """批量操作意图类型"""
    try:
        result = await batch_operation_api(operation_data.dict())
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"批量操作失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post(
    "/export",
    response_model=BaseResponse,
    summary="导出意图数据",
    description="""
    导出自定义意图类型数据。

    **参数说明：**
    - intent_ids: 指定导出的意图ID列表（可选，为空则导出全部）

    **返回数据：**
    - intents: 意图类型列表
    - patterns: 模式列表
    - examples: 示例列表
    - export_time: 导出时间
    - version: 数据版本
    """
)
async def export_intents(intent_ids: Optional[List[str]] = Body(None, description="意图ID列表")):
    """导出意图数据"""
    try:
        result = await export_intents_api(intent_ids)
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"导出意图数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post(
    "/import",
    response_model=BaseResponse,
    summary="导入意图数据",
    description="""
    导入自定义意图类型数据。

    **参数说明：**
    - data: 导入数据
    - overwrite_existing: 是否覆盖已存在的数据
    - validate_only: 仅验证不导入

    **返回数据：**
    - imported: 导入成功数量
    - skipped: 跳过数量
    - errors: 错误列表
    """
)
async def import_intents(import_data: IntentImportRequest):
    """导入意图数据"""
    try:
        result = await import_intents_api(import_data.dict())
        return BaseResponse(**result)
    except Exception as e:
        logger.error(f"导入意图数据失败: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# 导出路由器
__all__ = ["router"]
