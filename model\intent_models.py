"""
自定义意图类型数据模型
"""
from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field
from datetime import datetime
from .base_models import TimestampModel, MetadataModel, PaginationModel


class CustomIntentType(BaseModel):
    """自定义意图类型模型"""
    intent_id: str = Field(..., description="意图类型ID")
    name: str = Field(..., description="意图类型名称")
    display_name: str = Field(..., description="显示名称")
    description: Optional[str] = Field(None, description="意图描述")
    category: str = Field(default="custom", description="意图分类")
    priority: int = Field(default=100, ge=1, le=1000, description="优先级(1-1000)")
    is_active: bool = Field(default=True, description="是否启用")
    is_system: bool = Field(default=False, description="是否为系统内置")
    created_by: Optional[str] = Field(None, description="创建者")
    updated_by: Optional[str] = Field(None, description="更新者")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")


class IntentPattern(BaseModel):
    """意图识别模式"""
    pattern_id: str = Field(..., description="模式ID")
    intent_id: str = Field(..., description="关联的意图类型ID")
    pattern_type: Literal["keyword", "regex", "semantic"] = Field(..., description="模式类型")
    pattern_value: str = Field(..., description="模式值")
    weight: float = Field(default=1.0, ge=0.0, le=10.0, description="权重")
    is_active: bool = Field(default=True, description="是否启用")
    language: str = Field(default="zh", description="语言")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class IntentExample(BaseModel):
    """意图示例"""
    example_id: str = Field(..., description="示例ID")
    intent_id: str = Field(..., description="关联的意图类型ID")
    query_text: str = Field(..., description="查询文本")
    expected_confidence: float = Field(default=0.8, ge=0.0, le=1.0, description="期望置信度")
    is_positive: bool = Field(default=True, description="是否为正例")
    language: str = Field(default="zh", description="语言")
    created_at: str = Field(..., description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")


class IntentRecognitionResult(BaseModel):
    """意图识别结果"""
    intent_id: str = Field(..., description="识别的意图类型ID")
    intent_name: str = Field(..., description="意图类型名称")
    confidence: float = Field(..., ge=0.0, le=1.0, description="置信度")
    matched_patterns: List[str] = Field(default_factory=list, description="匹配的模式")
    reasoning: Optional[str] = Field(None, description="识别推理过程")
    is_custom: bool = Field(default=False, description="是否为自定义意图")


class CustomIntentCreateRequest(BaseModel):
    """创建自定义意图类型请求"""
    name: str = Field(..., min_length=1, max_length=100, description="意图类型名称")
    display_name: str = Field(..., min_length=1, max_length=200, description="显示名称")
    description: Optional[str] = Field(None, max_length=1000, description="意图描述")
    category: str = Field(default="custom", max_length=50, description="意图分类")
    priority: int = Field(default=100, ge=1, le=1000, description="优先级")
    patterns: List[Dict[str, Any]] = Field(default_factory=list, description="识别模式")
    examples: List[Dict[str, Any]] = Field(default_factory=list, description="示例数据")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")


class CustomIntentUpdateRequest(BaseModel):
    """更新自定义意图类型请求"""
    display_name: Optional[str] = Field(None, min_length=1, max_length=200, description="显示名称")
    description: Optional[str] = Field(None, max_length=1000, description="意图描述")
    category: Optional[str] = Field(None, max_length=50, description="意图分类")
    priority: Optional[int] = Field(None, ge=1, le=1000, description="优先级")
    is_active: Optional[bool] = Field(None, description="是否启用")
    metadata: Optional[Dict[str, Any]] = Field(None, description="扩展元数据")


class IntentPatternCreateRequest(BaseModel):
    """创建意图模式请求"""
    pattern_type: Literal["keyword", "regex", "semantic"] = Field(..., description="模式类型")
    pattern_value: str = Field(..., min_length=1, max_length=500, description="模式值")
    weight: float = Field(default=1.0, ge=0.0, le=10.0, description="权重")
    language: str = Field(default="zh", description="语言")


class IntentExampleCreateRequest(BaseModel):
    """创建意图示例请求"""
    query_text: str = Field(..., min_length=1, max_length=1000, description="查询文本")
    expected_confidence: float = Field(default=0.8, ge=0.0, le=1.0, description="期望置信度")
    is_positive: bool = Field(default=True, description="是否为正例")
    language: str = Field(default="zh", description="语言")


class CustomIntentListResponse(BaseModel):
    """自定义意图类型列表响应"""
    items: List[CustomIntentType] = Field(..., description="意图类型列表")
    pagination: PaginationModel = Field(..., description="分页信息")
    total_count: int = Field(..., description="总数量")
    active_count: int = Field(..., description="启用数量")


class IntentPatternListResponse(BaseModel):
    """意图模式列表响应"""
    items: List[IntentPattern] = Field(..., description="模式列表")
    pagination: PaginationModel = Field(..., description="分页信息")


class IntentExampleListResponse(BaseModel):
    """意图示例列表响应"""
    items: List[IntentExample] = Field(..., description="示例列表")
    pagination: PaginationModel = Field(..., description="分页信息")


class IntentTestRequest(BaseModel):
    """意图测试请求"""
    query_text: str = Field(..., min_length=1, max_length=1000, description="测试查询文本")
    intent_ids: Optional[List[str]] = Field(None, description="指定测试的意图ID列表")
    include_system: bool = Field(default=True, description="是否包含系统内置意图")
    include_custom: bool = Field(default=True, description="是否包含自定义意图")


class IntentTestResponse(BaseModel):
    """意图测试响应"""
    query_text: str = Field(..., description="测试查询文本")
    results: List[IntentRecognitionResult] = Field(..., description="识别结果列表")
    best_match: Optional[IntentRecognitionResult] = Field(None, description="最佳匹配")
    processing_time: float = Field(..., description="处理时间(秒)")


class IntentStatistics(BaseModel):
    """意图统计信息"""
    total_intents: int = Field(..., description="总意图数量")
    active_intents: int = Field(..., description="启用意图数量")
    custom_intents: int = Field(..., description="自定义意图数量")
    system_intents: int = Field(..., description="系统意图数量")
    total_patterns: int = Field(..., description="总模式数量")
    total_examples: int = Field(..., description="总示例数量")
    categories: Dict[str, int] = Field(default_factory=dict, description="分类统计")


class IntentBatchOperation(BaseModel):
    """意图批量操作"""
    operation: Literal["activate", "deactivate", "delete", "export"] = Field(..., description="操作类型")
    intent_ids: List[str] = Field(..., min_items=1, description="意图ID列表")
    options: Optional[Dict[str, Any]] = Field(None, description="操作选项")


class IntentExportData(BaseModel):
    """意图导出数据"""
    intents: List[CustomIntentType] = Field(..., description="意图类型列表")
    patterns: List[IntentPattern] = Field(..., description="模式列表")
    examples: List[IntentExample] = Field(..., description="示例列表")
    export_time: str = Field(..., description="导出时间")
    version: str = Field(default="1.0", description="数据版本")


class IntentImportRequest(BaseModel):
    """意图导入请求"""
    data: IntentExportData = Field(..., description="导入数据")
    overwrite_existing: bool = Field(default=False, description="是否覆盖已存在的数据")
    validate_only: bool = Field(default=False, description="仅验证不导入")


# 导出所有意图相关模型
__all__ = [
    "CustomIntentType",
    "IntentPattern",
    "IntentExample",
    "IntentRecognitionResult",
    "CustomIntentCreateRequest",
    "CustomIntentUpdateRequest",
    "IntentPatternCreateRequest",
    "IntentExampleCreateRequest",
    "CustomIntentListResponse",
    "IntentPatternListResponse",
    "IntentExampleListResponse",
    "IntentTestRequest",
    "IntentTestResponse",
    "IntentStatistics",
    "IntentBatchOperation",
    "IntentExportData",
    "IntentImportRequest"
]
