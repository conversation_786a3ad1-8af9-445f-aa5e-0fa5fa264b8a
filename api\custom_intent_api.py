"""
自定义意图类型API
提供自定义意图类型的REST API接口
"""
import time
from typing import Dict, Any, List, Optional

from common.logging_utils import logger_manager
from core.intent_recognition.custom_intent_manager import CustomIntentManager
from model.intent_models import (
    CustomIntentCreateRequest, CustomIntentUpdateRequest,
    IntentPatternCreateRequest, IntentExampleCreateRequest,
    IntentTestRequest, IntentBatchOperation, IntentImportRequest
)

logger = logger_manager.get_logger("custom_intent_api")

# 全局自定义意图管理器实例
custom_intent_manager = None


async def get_custom_intent_manager() -> CustomIntentManager:
    """获取自定义意图管理器实例"""
    global custom_intent_manager
    
    if custom_intent_manager is None:
        # 创建配置
        config = {
            "storage_type": "json",
            "storage_config": {
                "storage_dir": "./data/custom_intents"
            },
            "cache_ttl": 300
        }
        custom_intent_manager = CustomIntentManager(config=config)
        await custom_intent_manager.initialize()
        logger.info("自定义意图管理器初始化完成")
    
    return custom_intent_manager


# 意图类型管理API
async def create_intent_api(intent_data: Dict[str, Any], created_by: str = None) -> Dict[str, Any]:
    """创建自定义意图类型API"""
    try:
        start_time = time.time()
        manager = await get_custom_intent_manager()
        
        # 验证和转换请求数据
        create_request = CustomIntentCreateRequest(**intent_data)
        
        # 创建意图
        intent = await manager.create_intent(create_request, created_by)
        
        processing_time = time.time() - start_time
        
        return {
            "success": True,
            "data": intent.dict(),
            "message": "自定义意图类型创建成功",
            "processing_time": processing_time
        }
        
    except Exception as e:
        logger.error(f"创建自定义意图类型失败: {e}", exc_info=True)
        raise Exception(f"创建自定义意图类型失败: {str(e)}")


async def get_intent_api(intent_id: str) -> Dict[str, Any]:
    """获取自定义意图类型API"""
    try:
        manager = await get_custom_intent_manager()
        
        intent = await manager.get_intent(intent_id)
        if not intent:
            return {
                "success": False,
                "message": f"意图类型不存在: {intent_id}",
                "data": None
            }
        
        return {
            "success": True,
            "data": intent.dict(),
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取自定义意图类型失败: {e}")
        raise Exception(f"获取自定义意图类型失败: {str(e)}")


async def update_intent_api(intent_id: str, update_data: Dict[str, Any], updated_by: str = None) -> Dict[str, Any]:
    """更新自定义意图类型API"""
    try:
        start_time = time.time()
        manager = await get_custom_intent_manager()
        
        # 验证和转换请求数据
        update_request = CustomIntentUpdateRequest(**update_data)
        
        # 更新意图
        intent = await manager.update_intent(intent_id, update_request, updated_by)
        if not intent:
            return {
                "success": False,
                "message": f"意图类型不存在: {intent_id}",
                "data": None
            }
        
        processing_time = time.time() - start_time
        
        return {
            "success": True,
            "data": intent.dict(),
            "message": "更新成功",
            "processing_time": processing_time
        }
        
    except Exception as e:
        logger.error(f"更新自定义意图类型失败: {e}")
        raise Exception(f"更新自定义意图类型失败: {str(e)}")


async def delete_intent_api(intent_id: str) -> Dict[str, Any]:
    """删除自定义意图类型API"""
    try:
        start_time = time.time()
        manager = await get_custom_intent_manager()
        
        success = await manager.delete_intent(intent_id)
        if not success:
            return {
                "success": False,
                "message": f"意图类型不存在或删除失败: {intent_id}"
            }
        
        processing_time = time.time() - start_time
        
        return {
            "success": True,
            "message": "删除成功",
            "processing_time": processing_time
        }
        
    except Exception as e:
        logger.error(f"删除自定义意图类型失败: {e}")
        raise Exception(f"删除自定义意图类型失败: {str(e)}")


async def list_intents_api(page: int = 1, page_size: int = 20, category: str = None, is_active: bool = None) -> Dict[str, Any]:
    """列出自定义意图类型API"""
    try:
        manager = await get_custom_intent_manager()
        
        intents, total = await manager.list_intents(page, page_size, category, is_active)
        
        return {
            "success": True,
            "data": {
                "items": [intent.dict() for intent in intents],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total,
                    "total_pages": (total + page_size - 1) // page_size
                },
                "total_count": total,
                "active_count": sum(1 for intent in intents if intent.is_active)
            },
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"列出自定义意图类型失败: {e}")
        raise Exception(f"列出自定义意图类型失败: {str(e)}")


# 意图模式管理API
async def add_pattern_api(intent_id: str, pattern_data: Dict[str, Any]) -> Dict[str, Any]:
    """添加意图模式API"""
    try:
        manager = await get_custom_intent_manager()
        
        pattern_request = IntentPatternCreateRequest(**pattern_data)
        pattern = await manager.add_pattern(intent_id, pattern_request)
        
        return {
            "success": True,
            "data": pattern.dict(),
            "message": "模式添加成功"
        }
        
    except Exception as e:
        logger.error(f"添加意图模式失败: {e}")
        raise Exception(f"添加意图模式失败: {str(e)}")


async def get_patterns_api(intent_id: str) -> Dict[str, Any]:
    """获取意图模式API"""
    try:
        manager = await get_custom_intent_manager()
        
        patterns = await manager.get_patterns(intent_id)
        
        return {
            "success": True,
            "data": {
                "items": [pattern.dict() for pattern in patterns],
                "total_count": len(patterns)
            },
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取意图模式失败: {e}")
        raise Exception(f"获取意图模式失败: {str(e)}")


async def update_pattern_api(pattern_id: str, pattern_data: Dict[str, Any]) -> Dict[str, Any]:
    """更新意图模式API"""
    try:
        manager = await get_custom_intent_manager()
        
        pattern = await manager.update_pattern(pattern_id, pattern_data)
        if not pattern:
            return {
                "success": False,
                "message": f"模式不存在: {pattern_id}",
                "data": None
            }
        
        return {
            "success": True,
            "data": pattern.dict(),
            "message": "更新成功"
        }
        
    except Exception as e:
        logger.error(f"更新意图模式失败: {e}")
        raise Exception(f"更新意图模式失败: {str(e)}")


async def delete_pattern_api(pattern_id: str) -> Dict[str, Any]:
    """删除意图模式API"""
    try:
        manager = await get_custom_intent_manager()
        
        success = await manager.delete_pattern(pattern_id)
        if not success:
            return {
                "success": False,
                "message": f"模式不存在或删除失败: {pattern_id}"
            }
        
        return {
            "success": True,
            "message": "删除成功"
        }
        
    except Exception as e:
        logger.error(f"删除意图模式失败: {e}")
        raise Exception(f"删除意图模式失败: {str(e)}")


# 意图示例管理API
async def add_example_api(intent_id: str, example_data: Dict[str, Any]) -> Dict[str, Any]:
    """添加意图示例API"""
    try:
        manager = await get_custom_intent_manager()
        
        example_request = IntentExampleCreateRequest(**example_data)
        example = await manager.add_example(intent_id, example_request)
        
        return {
            "success": True,
            "data": example.dict(),
            "message": "示例添加成功"
        }
        
    except Exception as e:
        logger.error(f"添加意图示例失败: {e}")
        raise Exception(f"添加意图示例失败: {str(e)}")


async def get_examples_api(intent_id: str) -> Dict[str, Any]:
    """获取意图示例API"""
    try:
        manager = await get_custom_intent_manager()
        
        examples = await manager.get_examples(intent_id)
        
        return {
            "success": True,
            "data": {
                "items": [example.dict() for example in examples],
                "total_count": len(examples)
            },
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取意图示例失败: {e}")
        raise Exception(f"获取意图示例失败: {str(e)}")


async def delete_example_api(example_id: str) -> Dict[str, Any]:
    """删除意图示例API"""
    try:
        manager = await get_custom_intent_manager()
        
        success = await manager.delete_example(example_id)
        if not success:
            return {
                "success": False,
                "message": f"示例不存在或删除失败: {example_id}"
            }
        
        return {
            "success": True,
            "message": "删除成功"
        }
        
    except Exception as e:
        logger.error(f"删除意图示例失败: {e}")
        raise Exception(f"删除意图示例失败: {str(e)}")


# 意图测试和统计API
async def test_intent_api(test_data: Dict[str, Any]) -> Dict[str, Any]:
    """测试意图识别API"""
    try:
        start_time = time.time()
        manager = await get_custom_intent_manager()

        test_request = IntentTestRequest(**test_data)
        result = await manager.test_intent_recognition(test_request)

        processing_time = time.time() - start_time

        return {
            "success": True,
            "data": result.dict(),
            "message": "测试完成",
            "processing_time": processing_time
        }

    except Exception as e:
        logger.error(f"意图测试失败: {e}")
        raise Exception(f"意图测试失败: {str(e)}")


async def get_statistics_api() -> Dict[str, Any]:
    """获取意图统计信息API"""
    try:
        manager = await get_custom_intent_manager()

        stats = await manager.get_statistics()

        return {
            "success": True,
            "data": stats.dict(),
            "message": "获取统计信息成功"
        }

    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise Exception(f"获取统计信息失败: {str(e)}")


async def batch_operation_api(operation_data: Dict[str, Any]) -> Dict[str, Any]:
    """批量操作API"""
    try:
        start_time = time.time()
        manager = await get_custom_intent_manager()

        batch_operation = IntentBatchOperation(**operation_data)
        results = await manager.batch_operation(batch_operation)

        processing_time = time.time() - start_time

        return {
            "success": True,
            "data": results,
            "message": "批量操作完成",
            "processing_time": processing_time
        }

    except Exception as e:
        logger.error(f"批量操作失败: {e}")
        raise Exception(f"批量操作失败: {str(e)}")


async def export_intents_api(intent_ids: List[str] = None) -> Dict[str, Any]:
    """导出意图数据API"""
    try:
        start_time = time.time()
        manager = await get_custom_intent_manager()

        export_data = await manager.export_intents(intent_ids)

        processing_time = time.time() - start_time

        return {
            "success": True,
            "data": export_data.dict(),
            "message": "导出完成",
            "processing_time": processing_time
        }

    except Exception as e:
        logger.error(f"导出意图数据失败: {e}")
        raise Exception(f"导出意图数据失败: {str(e)}")


async def import_intents_api(import_data: Dict[str, Any]) -> Dict[str, Any]:
    """导入意图数据API"""
    try:
        start_time = time.time()
        manager = await get_custom_intent_manager()

        import_request = IntentImportRequest(**import_data)
        results = await manager.import_intents(import_request)

        processing_time = time.time() - start_time

        return {
            "success": True,
            "data": results,
            "message": "导入完成",
            "processing_time": processing_time
        }

    except Exception as e:
        logger.error(f"导入意图数据失败: {e}")
        raise Exception(f"导入意图数据失败: {str(e)}")


async def health_check_api() -> Dict[str, Any]:
    """健康检查API"""
    try:
        manager = await get_custom_intent_manager()

        # 获取基本统计信息
        stats = await manager.get_statistics()

        return {
            "success": True,
            "data": {
                "status": "healthy",
                "service": "custom_intent_management",
                "version": "1.0.0",
                "timestamp": time.time(),
                "statistics": {
                    "total_intents": stats.total_intents,
                    "active_intents": stats.active_intents,
                    "custom_intents": stats.custom_intents
                }
            },
            "message": "服务正常"
        }

    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "success": False,
            "message": f"服务异常: {str(e)}",
            "data": {
                "status": "unhealthy",
                "service": "custom_intent_management",
                "timestamp": time.time()
            }
        }


# 导出所有API函数
__all__ = [
    "get_custom_intent_manager",
    # 意图类型管理
    "create_intent_api",
    "get_intent_api",
    "update_intent_api",
    "delete_intent_api",
    "list_intents_api",
    # 意图模式管理
    "add_pattern_api",
    "get_patterns_api",
    "update_pattern_api",
    "delete_pattern_api",
    # 意图示例管理
    "add_example_api",
    "get_examples_api",
    "delete_example_api",
    # 测试和统计
    "test_intent_api",
    "get_statistics_api",
    "batch_operation_api",
    "export_intents_api",
    "import_intents_api",
    "health_check_api"
]
