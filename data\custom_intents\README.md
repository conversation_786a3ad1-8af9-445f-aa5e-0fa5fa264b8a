# 自定义意图类型数据存储

这个目录用于存储自定义意图类型的数据文件。

## 文件结构

- `intents.json` - 存储自定义意图类型的基本信息
- `patterns.json` - 存储意图识别模式
- `examples.json` - 存储意图示例数据

## 数据格式

### intents.json
```json
[
  {
    "intent_id": "uuid",
    "name": "intent_name",
    "display_name": "显示名称",
    "description": "意图描述",
    "category": "分类",
    "priority": 100,
    "is_active": true,
    "is_system": false,
    "created_by": "用户ID",
    "created_at": "2024-01-01T00:00:00",
    "updated_at": null,
    "metadata": {}
  }
]
```

### patterns.json
```json
[
  {
    "pattern_id": "uuid",
    "intent_id": "关联的意图ID",
    "pattern_type": "keyword|regex|semantic",
    "pattern_value": "模式值",
    "weight": 1.0,
    "is_active": true,
    "language": "zh",
    "created_at": "2024-01-01T00:00:00",
    "updated_at": null
  }
]
```

### examples.json
```json
[
  {
    "example_id": "uuid",
    "intent_id": "关联的意图ID",
    "query_text": "示例查询文本",
    "expected_confidence": 0.8,
    "is_positive": true,
    "language": "zh",
    "created_at": "2024-01-01T00:00:00",
    "updated_at": null
  }
]
```

## 注意事项

1. 这些文件会在系统首次运行时自动创建
2. 请不要手动编辑这些文件，使用API进行操作
3. 建议定期备份这些数据文件
4. 如果需要迁移数据，可以使用导出/导入功能
