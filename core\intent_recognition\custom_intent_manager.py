"""
自定义意图类型管理器
实现自定义意图类型的业务逻辑和管理功能
"""
import re
import asyncio
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

from common.logging_utils import logger_manager
from model.intent_models import (
    CustomIntentType, IntentPattern, IntentExample, IntentRecognitionResult,
    CustomIntentCreateRequest, CustomIntentUpdateRequest,
    IntentPatternCreateRequest, IntentExampleCreateRequest,
    IntentTestRequest, IntentTestResponse, IntentStatistics,
    IntentBatchOperation, IntentExportData, IntentImportRequest
)
from .custom_intent_storage import BaseIntentStorage, IntentStorageFactory

logger = logger_manager.get_logger("custom_intent_manager")


class CustomIntentManager:
    """自定义意图类型管理器"""
    
    def __init__(self, storage: BaseIntentStorage = None, config: Dict[str, Any] = None):
        self.config = config or {}
        self.storage = storage or IntentStorageFactory.create_storage(
            storage_type=self.config.get("storage_type", "json"),
            config=self.config.get("storage_config", {})
        )
        self._initialized = False
        self._cache = {}
        self._cache_ttl = self.config.get("cache_ttl", 300)  # 5分钟缓存
        self._last_cache_update = 0
    
    async def initialize(self) -> bool:
        """初始化管理器"""
        if self._initialized:
            return True
        
        try:
            success = await self.storage.initialize()
            if success:
                self._initialized = True
                await self._refresh_cache()
                logger.info("自定义意图管理器初始化成功")
            return success
        except Exception as e:
            logger.error(f"自定义意图管理器初始化失败: {e}")
            return False
    
    async def _refresh_cache(self):
        """刷新缓存"""
        try:
            intents, _ = await self.storage.list_intents(page=1, page_size=1000)
            self._cache = {intent.intent_id: intent for intent in intents}
            self._last_cache_update = datetime.now().timestamp()
            logger.debug(f"缓存刷新完成，加载 {len(self._cache)} 个意图类型")
        except Exception as e:
            logger.error(f"缓存刷新失败: {e}")
    
    async def _ensure_cache_fresh(self):
        """确保缓存新鲜"""
        current_time = datetime.now().timestamp()
        if current_time - self._last_cache_update > self._cache_ttl:
            await self._refresh_cache()
    
    # 意图类型管理
    async def create_intent(self, intent_data: CustomIntentCreateRequest, created_by: str = None) -> CustomIntentType:
        """创建自定义意图类型"""
        if not self._initialized:
            await self.initialize()
        
        # 验证数据
        await self._validate_intent_data(intent_data)
        
        # 创建意图
        intent = await self.storage.create_intent(intent_data, created_by)
        
        # 更新缓存
        self._cache[intent.intent_id] = intent
        
        logger.info(f"创建自定义意图类型: {intent.name} ({intent.intent_id})")
        return intent
    
    async def get_intent(self, intent_id: str) -> Optional[CustomIntentType]:
        """获取意图类型"""
        if not self._initialized:
            await self.initialize()
        
        # 先从缓存获取
        await self._ensure_cache_fresh()
        if intent_id in self._cache:
            return self._cache[intent_id]
        
        # 从存储获取
        intent = await self.storage.get_intent(intent_id)
        if intent:
            self._cache[intent_id] = intent
        
        return intent
    
    async def update_intent(self, intent_id: str, update_data: CustomIntentUpdateRequest, updated_by: str = None) -> Optional[CustomIntentType]:
        """更新意图类型"""
        if not self._initialized:
            await self.initialize()
        
        # 检查是否存在
        existing = await self.get_intent(intent_id)
        if not existing:
            return None
        
        # 检查是否为系统内置
        if existing.is_system:
            raise ValueError("不能修改系统内置意图类型")
        
        # 更新
        intent = await self.storage.update_intent(intent_id, update_data, updated_by)
        if intent:
            self._cache[intent_id] = intent
        
        logger.info(f"更新意图类型: {intent_id}")
        return intent
    
    async def delete_intent(self, intent_id: str) -> bool:
        """删除意图类型"""
        if not self._initialized:
            await self.initialize()
        
        # 检查是否存在
        existing = await self.get_intent(intent_id)
        if not existing:
            return False
        
        # 检查是否为系统内置
        if existing.is_system:
            raise ValueError("不能删除系统内置意图类型")
        
        # 删除
        success = await self.storage.delete_intent(intent_id)
        if success:
            self._cache.pop(intent_id, None)
        
        logger.info(f"删除意图类型: {intent_id}")
        return success
    
    async def list_intents(self, page: int = 1, page_size: int = 20, category: str = None, is_active: bool = None) -> Tuple[List[CustomIntentType], int]:
        """列出意图类型"""
        if not self._initialized:
            await self.initialize()
        
        return await self.storage.list_intents(page, page_size, category, is_active)
    
    # 意图模式管理
    async def add_pattern(self, intent_id: str, pattern_data: IntentPatternCreateRequest) -> IntentPattern:
        """添加意图模式"""
        if not self._initialized:
            await self.initialize()
        
        # 验证意图是否存在
        intent = await self.get_intent(intent_id)
        if not intent:
            raise ValueError(f"意图类型不存在: {intent_id}")
        
        # 验证模式
        await self._validate_pattern(pattern_data)
        
        return await self.storage.create_pattern(intent_id, pattern_data)
    
    async def get_patterns(self, intent_id: str) -> List[IntentPattern]:
        """获取意图模式"""
        if not self._initialized:
            await self.initialize()
        
        return await self.storage.get_patterns(intent_id)
    
    async def update_pattern(self, pattern_id: str, pattern_data: Dict[str, Any]) -> Optional[IntentPattern]:
        """更新意图模式"""
        if not self._initialized:
            await self.initialize()
        
        return await self.storage.update_pattern(pattern_id, pattern_data)
    
    async def delete_pattern(self, pattern_id: str) -> bool:
        """删除意图模式"""
        if not self._initialized:
            await self.initialize()
        
        return await self.storage.delete_pattern(pattern_id)
    
    # 意图示例管理
    async def add_example(self, intent_id: str, example_data: IntentExampleCreateRequest) -> IntentExample:
        """添加意图示例"""
        if not self._initialized:
            await self.initialize()
        
        # 验证意图是否存在
        intent = await self.get_intent(intent_id)
        if not intent:
            raise ValueError(f"意图类型不存在: {intent_id}")
        
        return await self.storage.create_example(intent_id, example_data)
    
    async def get_examples(self, intent_id: str) -> List[IntentExample]:
        """获取意图示例"""
        if not self._initialized:
            await self.initialize()
        
        return await self.storage.get_examples(intent_id)
    
    async def delete_example(self, example_id: str) -> bool:
        """删除意图示例"""
        if not self._initialized:
            await self.initialize()
        
        return await self.storage.delete_example(example_id)
    
    # 意图识别
    async def recognize_intent(self, query_text: str, intent_ids: List[str] = None) -> List[IntentRecognitionResult]:
        """识别查询的意图"""
        if not self._initialized:
            await self.initialize()
        
        await self._ensure_cache_fresh()
        
        results = []
        target_intents = []
        
        # 确定要检查的意图
        if intent_ids:
            target_intents = [self._cache[iid] for iid in intent_ids if iid in self._cache]
        else:
            target_intents = [intent for intent in self._cache.values() if intent.is_active]
        
        # 对每个意图进行匹配
        for intent in target_intents:
            confidence = await self._calculate_intent_confidence(query_text, intent)
            if confidence > 0:
                result = IntentRecognitionResult(
                    intent_id=intent.intent_id,
                    intent_name=intent.name,
                    confidence=confidence,
                    matched_patterns=[],
                    reasoning=f"基于模式匹配的置信度: {confidence:.3f}",
                    is_custom=True
                )
                results.append(result)
        
        # 按置信度排序
        results.sort(key=lambda x: x.confidence, reverse=True)
        
        return results
    
    async def _calculate_intent_confidence(self, query_text: str, intent: CustomIntentType) -> float:
        """计算意图匹配置信度"""
        patterns = await self.get_patterns(intent.intent_id)
        if not patterns:
            return 0.0
        
        total_weight = 0.0
        matched_weight = 0.0
        
        query_lower = query_text.lower()
        
        for pattern in patterns:
            if not pattern.is_active:
                continue
            
            total_weight += pattern.weight
            
            if pattern.pattern_type == "keyword":
                if pattern.pattern_value.lower() in query_lower:
                    matched_weight += pattern.weight
            elif pattern.pattern_type == "regex":
                try:
                    if re.search(pattern.pattern_value, query_text, re.IGNORECASE):
                        matched_weight += pattern.weight
                except re.error:
                    logger.warning(f"无效的正则表达式: {pattern.pattern_value}")
            # semantic类型需要向量相似度计算，这里简化处理
            elif pattern.pattern_type == "semantic":
                # 简单的关键词匹配作为语义匹配的近似
                keywords = pattern.pattern_value.split()
                matched_keywords = sum(1 for kw in keywords if kw.lower() in query_lower)
                if matched_keywords > 0:
                    matched_weight += pattern.weight * (matched_keywords / len(keywords))
        
        return matched_weight / total_weight if total_weight > 0 else 0.0
    
    async def _validate_intent_data(self, intent_data: CustomIntentCreateRequest):
        """验证意图数据"""
        # 检查名称格式
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', intent_data.name):
            raise ValueError("意图名称只能包含字母、数字和下划线，且必须以字母开头")
        
        # 检查名称是否已存在
        await self._ensure_cache_fresh()
        for intent in self._cache.values():
            if intent.name == intent_data.name:
                raise ValueError(f"意图名称已存在: {intent_data.name}")
    
    async def _validate_pattern(self, pattern_data: IntentPatternCreateRequest):
        """验证模式数据"""
        if pattern_data.pattern_type == "regex":
            try:
                re.compile(pattern_data.pattern_value)
            except re.error as e:
                raise ValueError(f"无效的正则表达式: {e}")


    # 统计和批量操作
    async def get_statistics(self) -> IntentStatistics:
        """获取意图统计信息"""
        if not self._initialized:
            await self.initialize()

        await self._ensure_cache_fresh()

        total_intents = len(self._cache)
        active_intents = sum(1 for intent in self._cache.values() if intent.is_active)
        custom_intents = sum(1 for intent in self._cache.values() if not intent.is_system)
        system_intents = total_intents - custom_intents

        # 统计分类
        categories = {}
        for intent in self._cache.values():
            category = intent.category or "未分类"
            categories[category] = categories.get(category, 0) + 1

        # 统计模式和示例数量（简化实现）
        total_patterns = 0
        total_examples = 0
        for intent in self._cache.values():
            patterns = await self.get_patterns(intent.intent_id)
            examples = await self.get_examples(intent.intent_id)
            total_patterns += len(patterns)
            total_examples += len(examples)

        return IntentStatistics(
            total_intents=total_intents,
            active_intents=active_intents,
            custom_intents=custom_intents,
            system_intents=system_intents,
            total_patterns=total_patterns,
            total_examples=total_examples,
            categories=categories
        )

    async def batch_operation(self, operation: IntentBatchOperation) -> Dict[str, Any]:
        """批量操作"""
        if not self._initialized:
            await self.initialize()

        results = {"success": [], "failed": []}

        for intent_id in operation.intent_ids:
            try:
                if operation.operation == "activate":
                    await self.update_intent(intent_id, CustomIntentUpdateRequest(is_active=True))
                    results["success"].append(intent_id)
                elif operation.operation == "deactivate":
                    await self.update_intent(intent_id, CustomIntentUpdateRequest(is_active=False))
                    results["success"].append(intent_id)
                elif operation.operation == "delete":
                    await self.delete_intent(intent_id)
                    results["success"].append(intent_id)
                elif operation.operation == "export":
                    # 导出操作在这里只是标记，实际导出由export_intents方法处理
                    results["success"].append(intent_id)
            except Exception as e:
                logger.error(f"批量操作失败 {operation.operation} {intent_id}: {e}")
                results["failed"].append({"intent_id": intent_id, "error": str(e)})

        return results

    async def export_intents(self, intent_ids: List[str] = None) -> IntentExportData:
        """导出意图数据"""
        if not self._initialized:
            await self.initialize()

        # 确定要导出的意图
        if intent_ids:
            intents = [await self.get_intent(iid) for iid in intent_ids]
            intents = [intent for intent in intents if intent]
        else:
            intents, _ = await self.list_intents(page=1, page_size=1000)

        # 导出关联的模式和示例
        all_patterns = []
        all_examples = []

        for intent in intents:
            patterns = await self.get_patterns(intent.intent_id)
            examples = await self.get_examples(intent.intent_id)
            all_patterns.extend(patterns)
            all_examples.extend(examples)

        return IntentExportData(
            intents=intents,
            patterns=all_patterns,
            examples=all_examples,
            export_time=datetime.now().isoformat(),
            version="1.0"
        )

    async def import_intents(self, import_request: IntentImportRequest) -> Dict[str, Any]:
        """导入意图数据"""
        if not self._initialized:
            await self.initialize()

        results = {"imported": 0, "skipped": 0, "errors": []}

        # 验证模式
        if import_request.validate_only:
            # 只验证不导入
            for intent in import_request.data.intents:
                try:
                    await self._validate_intent_data(CustomIntentCreateRequest(
                        name=intent.name,
                        display_name=intent.display_name,
                        description=intent.description,
                        category=intent.category,
                        priority=intent.priority
                    ))
                except Exception as e:
                    results["errors"].append(f"意图 {intent.name} 验证失败: {e}")
            return results

        # 导入意图
        for intent in import_request.data.intents:
            try:
                # 检查是否已存在
                existing = await self.get_intent(intent.intent_id)
                if existing and not import_request.overwrite_existing:
                    results["skipped"] += 1
                    continue

                # 创建或更新意图
                if existing and import_request.overwrite_existing:
                    await self.update_intent(
                        intent.intent_id,
                        CustomIntentUpdateRequest(
                            display_name=intent.display_name,
                            description=intent.description,
                            category=intent.category,
                            priority=intent.priority,
                            is_active=intent.is_active,
                            metadata=intent.metadata
                        )
                    )
                else:
                    await self.create_intent(CustomIntentCreateRequest(
                        name=intent.name,
                        display_name=intent.display_name,
                        description=intent.description,
                        category=intent.category,
                        priority=intent.priority,
                        metadata=intent.metadata
                    ))

                results["imported"] += 1

            except Exception as e:
                results["errors"].append(f"导入意图 {intent.name} 失败: {e}")

        return results

    async def test_intent_recognition(self, test_request: IntentTestRequest) -> IntentTestResponse:
        """测试意图识别"""
        start_time = datetime.now()

        # 识别意图
        results = await self.recognize_intent(
            test_request.query_text,
            test_request.intent_ids
        )

        # 过滤结果
        if not test_request.include_custom:
            results = [r for r in results if not r.is_custom]

        # 找到最佳匹配
        best_match = results[0] if results else None

        processing_time = (datetime.now() - start_time).total_seconds()

        return IntentTestResponse(
            query_text=test_request.query_text,
            results=results,
            best_match=best_match,
            processing_time=processing_time
        )


# 导出
__all__ = ["CustomIntentManager"]
